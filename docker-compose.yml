version: '3.8'

services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.0.1
    container_name: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka:
    image: confluentinc/cp-kafka:7.0.1
    container_name: kafka
    ports:
      - "9092:9092"
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0

  clickhouse:
    image: clickhouse/clickhouse-server
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - ./clickhouse_data:/var/lib/clickhouse
    ulimits:
      nofile:
        soft: 262144
        hard: 262144

  mlflow:
    image: anishnair/mlflow
    ports:
      - "5000:5000"
    volumes:
      - ./mlflow_data:/mlflow
    environment:
      - MLFLOW_S3_ENDPOINT_URL=http://minio:9000
      - AWS_ACCESS_KEY_ID=minio
      - AWS_SECRET_ACCESS_KEY=minio123
      - MLFLOW_ARTIFACT_ROOT=s3://mlflow

  jupyterhub:
    build:
      context: ./jupyter
    ports:
      - "8000:8000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./jupyterhub_config.py:/srv/jupyterhub/jupyterhub_config.py
    environment:
      - DOCKER_NETWORK_NAME=host
      - JUPYTERHUB_AUTHENTICATOR=dummyauthenticator
      - JUPYTER_ENABLE_LAB=yes

  minio:
    image: minio/minio
    ports:
      - "9001:9000"
    volumes:
      - ./minio_data:/data
    environment:
      - MINIO_ROOT_USER=minio
      - MINIO_ROOT_PASSWORD=minio123
    command: server /data

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3001:3000"
  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    ports:
      - "9100:9100"
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro

  node-service:
    build:
      context: ./packages/node-service
    container_name: node-service
    ports:
      - "3000:3000"
    deploy:
      replicas: 2
    depends_on:
      - kafka
      - clickhouse

  strategy-manager:
    build:
      context: ./packages/strategy-manager
    container_name: strategy-manager
    ports:
      - "4003:4003"
    deploy:
      replicas: 2
    healthcheck:
        test: ["CMD", "curl", "-f", "http://localhost:4003/status"]
        interval: 30s
        timeout: 10s
        retries: 3
  portfolio-manager:
    build:
      context: ./packages/portfolio-manager
    container_name: portfolio-manager
    ports:
      - "4004:4004"
    deploy:
      replicas: 2
    healthcheck:
        test: ["CMD", "curl", "-f", "http://localhost:4004/status"]
        interval: 30s
        timeout: 10s
        retries: 3
  global-risk-manager:
    build:
      context: ./packages/global-risk-manager
    container_name: global-risk-manager
    ports:
      - "4005:4005"
    deploy:
      replicas: 2
    healthcheck:
        test: ["CMD", "curl", "-f", "http://localhost:4005/status"]
        interval: 30s
        timeout: 10s
        retries: 3
  live-api:
    build:
      context: ./packages/live-api
    container_name: live-api
    ports:
      - "4000:4000"
    deploy:
      replicas: 2
    healthcheck:
        test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
        interval: 30s
        timeout: 10s
        retries: 3
    depends_on:
        - strategy-manager
        - portfolio-manager
        - global-risk-manager
