# Technology Stack

## Backend Services
- **Language**: TypeScript/Node.js for microservices
- **Framework**: Express.js for REST APIs
- **Testing**: Jest with Supertest for API testing
- **Build**: TypeScript compiler (`tsc`)

## Data & Analytics
- **Python Stack**: pandas, numpy, scipy, scikit-learn
- **ML Frameworks**: PyTorch, TensorFlow, Keras
- **Backtesting**: VectorBT for quantitative analysis
- **Visualization**: mat<PERSON><PERSON><PERSON>b, seaborn, plotly
- **Explainable AI**: SHAP, LIME for model interpretability

## Infrastructure
- **Database**: ClickHouse (time-series data), MinIO (object storage)
- **Message Queue**: Apache Kafka for real-time data streaming
- **Monitoring**: Prometheus + Grafana + Node Exporter + cAdvisor
- **ML Ops**: MLflow for experiment tracking
- **Research**: JupyterHub for collaborative development
- **Containerization**: Docker with docker-compose

## Frontend
- **Framework**: React 19 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Charts**: Recharts
- **Testing**: Vitest + Testing Library

## Blockchain Integration
- **Web3 Library**: ethers.js for Ethereum interaction
- **Node Connection**: Custom blockchain node service

## Common Commands

### Development
```bash
# Start all services
docker-compose up -d

# Frontend development
cd frontend && npm run dev

# Backend service development (example: node-service)
cd packages/node-service && npm run dev

# Run tests
npm test
```

### Build & Deploy
```bash
# Build TypeScript services
npm run build

# Build frontend
cd frontend && npm run build

# Run production
npm start
```

### Data & Research
```bash
# Start Jupyter environment
# Access via http://localhost:8000

# MLflow tracking
# Access via http://localhost:5000

# Monitoring dashboards
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3001
```