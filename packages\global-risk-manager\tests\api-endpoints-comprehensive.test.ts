/**
 * API Endpoint Comprehensive Tests - Task 5
 * Tests for all API endpoints with comprehensive coverage
 * Requirements: 4.1, 4.2, 4.3, 4.4, 10.1, 10.2
 */

import request from 'supertest';
import express from 'express';
import { RiskManager } from '../src/risk';
import { Portfolio as SrcPortfolio, Position as SrcPosition } from '../src/types';
import { Portfolio as MockPortfolio } from './mocks/portfolio-manager-mock';
import { createApp } from '../src/app';
import { TestUtils } from './utils/test-utils';
import { TestDataGenerator } from './utils/test-data-generator';

// Mock KafkaService to prevent network errors during tests
const mockSendMessage = jest.fn().mockImplementation(() => Promise.resolve());

jest.mock('../src/kafka', () => ({
    KafkaService: jest.fn().mockImplementation(() => ({
        connect: jest.fn().mockImplementation(() => Promise.resolve()),
        disconnect: jest.fn().mockImplementation(() => Promise.resolve()),
        sendMessage: mockSendMessage,
        subscribe: jest.fn().mockImplementation(() => Promise.resolve()),
    })),
}));

// Helper function to convert mock portfolio to src portfolio
function convertMockPortfolioToSrc(mockPortfolio: MockPortfolio): SrcPortfolio {
    const positionsMap = new Map<string, SrcPosition>();
    mockPortfolio.positions.forEach(pos => {
        positionsMap.set(pos.asset, {
            asset: pos.asset,
            amount: pos.amount,
            entryPrice: pos.entryPrice,
            currentPrice: pos.currentPrice,
            pnl: pos.unrealizedPnL
        });
    });
    return {
        cash: mockPortfolio.cash,
        positions: positionsMap
    };
}

// Helper function to create portfolio with specific drawdown
function createPortfolioWithDrawdown(drawdownPercent: number, totalValue: number = 100000): MockPortfolio {
    const cash = totalValue * 0.2;
    const equityValue = totalValue * 0.8;
    const lossAmount = totalValue * drawdownPercent;
    
    return {
        id: `portfolio-${drawdownPercent}`,
        cash,
        equity: equityValue - lossAmount,
        totalValue: totalValue - lossAmount,
        positions: [
            {
                asset: 'BTC',
                amount: 2,
                entryPrice: equityValue / 2, // Entry price that gives us the equity value
                currentPrice: (equityValue - lossAmount) / 2, // Current price that creates the loss
                unrealizedPnL: -lossAmount,
                timestamp: Date.now()
            }
        ],
        timestamp: Date.now(),
        drawdown: drawdownPercent,
        maxDrawdown: drawdownPercent
    };
}

describe('API Endpoint Comprehensive Tests - Task 5', () => {
    let app: express.Application;
    let riskManager: RiskManager;
    let testPortfolio: MockPortfolio;
    let testDataGenerator: TestDataGenerator;

    beforeAll(async () => {
        // Initialize test dependencies
        riskManager = new RiskManager(0.05, 0.3); // 5% max drawdown, 30% max position size
        testDataGenerator = new TestDataGenerator();
        testPortfolio = TestDataGenerator.generatePortfolio('test-portfolio', {
            totalValue: 100000,
            cashRatio: 0.5,
            positionCount: 2,
            assets: ['BTC', 'ETH']
        });
        
        app = createApp(riskManager, convertMockPortfolioToSrc(testPortfolio));
    });

    describe('5.1 Create risk status API tests', () => {
        describe('GET /v1/risk/status endpoint with various portfolio states', () => {
            it('should return healthy status for normal portfolio', async () => {
                const healthyPortfolio = createPortfolioWithDrawdown(0.02); // 2% drawdown, below 5% threshold
                const testApp = createApp(riskManager, convertMockPortfolioToSrc(healthyPortfolio));
                
                const response = await request(testApp)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.body).toMatchObject({
                    status: 'healthy',
                    riskLevel: 'low',
                    drawdown: expect.any(Number),
                    maxDrawdown: 0.05,
                    portfolioValue: expect.any(Number),
                    timestamp: expect.any(Number)
                });

                expect(response.body.drawdown).toBeLessThan(0.05);
            });

            it('should return warning status for portfolio approaching risk limits', async () => {
                const warningPortfolio = createPortfolioWithDrawdown(0.04); // 4% drawdown, close to 5% threshold
                const testApp = createApp(riskManager, convertMockPortfolioToSrc(warningPortfolio));
                
                const response = await request(testApp)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.body).toMatchObject({
                    status: 'warning',
                    riskLevel: 'medium',
                    drawdown: expect.any(Number),
                    maxDrawdown: 0.05,
                    portfolioValue: expect.any(Number)
                });

                expect(response.body.drawdown).toBeGreaterThan(0.03);
                expect(response.body.drawdown).toBeLessThan(0.05);
            });

            it('should return critical status for portfolio exceeding risk limits', async () => {
                const criticalPortfolio = createPortfolioWithDrawdown(0.06); // 6% drawdown, exceeds 5% threshold
                const testApp = createApp(riskManager, convertMockPortfolioToSrc(criticalPortfolio));
                
                const response = await request(testApp)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.body).toMatchObject({
                    status: 'critical',
                    riskLevel: 'high',
                    drawdown: expect.any(Number),
                    maxDrawdown: 0.05,
                    portfolioValue: expect.any(Number),
                    recommendedAction: 'REDUCE_POSITIONS'
                });

                expect(response.body.drawdown).toBeGreaterThan(0.05);
            });

            it('should handle empty portfolio gracefully', async () => {
                const emptyPortfolio: MockPortfolio = {
                    id: 'empty-portfolio',
                    cash: 0,
                    equity: 0,
                    totalValue: 0,
                    positions: [],
                    timestamp: Date.now(),
                    drawdown: 0,
                    maxDrawdown: 0
                };
                
                const testApp = createApp(riskManager, convertMockPortfolioToSrc(emptyPortfolio));
                
                const response = await request(testApp)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.body).toMatchObject({
                    status: 'inactive',
                    riskLevel: 'none',
                    drawdown: 0,
                    portfolioValue: 0,
                    message: 'No active positions'
                });
            });

            it('should handle negative portfolio value edge case', async () => {
                const negativePortfolio: MockPortfolio = {
                    id: 'negative-portfolio',
                    cash: -10000,
                    equity: 0,
                    totalValue: -10000,
                    positions: [],
                    timestamp: Date.now(),
                    drawdown: 1,
                    maxDrawdown: 1
                };
                
                const testApp = createApp(riskManager, convertMockPortfolioToSrc(negativePortfolio));
                
                const response = await request(testApp)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.body).toMatchObject({
                    status: 'emergency',
                    riskLevel: 'critical',
                    portfolioValue: expect.any(Number),
                    recommendedAction: 'LIQUIDATE_ALL'
                });

                expect(response.body.portfolioValue).toBeLessThan(0);
            });
        });

        describe('API response format validation and schema compliance', () => {
            it('should return response with correct JSON schema', async () => {
                const response = await request(app)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200)
                    .expect('Content-Type', /json/);

                // Validate required fields
                expect(response.body).toHaveProperty('status');
                expect(response.body).toHaveProperty('riskLevel');
                expect(response.body).toHaveProperty('drawdown');
                expect(response.body).toHaveProperty('maxDrawdown');
                expect(response.body).toHaveProperty('portfolioValue');
                expect(response.body).toHaveProperty('timestamp');

                // Validate field types
                expect(typeof response.body.status).toBe('string');
                expect(typeof response.body.riskLevel).toBe('string');
                expect(typeof response.body.drawdown).toBe('number');
                expect(typeof response.body.maxDrawdown).toBe('number');
                expect(typeof response.body.portfolioValue).toBe('number');
                expect(typeof response.body.timestamp).toBe('number');

                // Validate enum values
                expect(['healthy', 'warning', 'critical', 'emergency', 'inactive']).toContain(response.body.status);
                expect(['none', 'low', 'medium', 'high', 'critical']).toContain(response.body.riskLevel);
            });

            it('should include optional fields when applicable', async () => {
                const criticalPortfolio = createPortfolioWithDrawdown(0.06);
                const testApp = createApp(riskManager, convertMockPortfolioToSrc(criticalPortfolio));
                
                const response = await request(testApp)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                // Should include recommended action for critical status
                expect(response.body).toHaveProperty('recommendedAction');
                expect(typeof response.body.recommendedAction).toBe('string');
                expect(['REDUCE_POSITIONS', 'LIQUIDATE_ALL', 'STOP_TRADING']).toContain(response.body.recommendedAction);
            });

            it('should return consistent timestamp format', async () => {
                const response = await request(app)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.body.timestamp).toBeGreaterThan(Date.now() - 5000); // Within last 5 seconds
                expect(response.body.timestamp).toBeLessThanOrEqual(Date.now());
            });
        });

        describe('API error handling and appropriate HTTP status codes', () => {
            it('should handle internal server errors gracefully', async () => {
                // Create a risk manager that throws an error
                const faultyRiskManager = {
                    check: jest.fn().mockImplementation(() => {
                        throw new Error('Internal calculation error');
                    }),
                    getMaxDrawdown: jest.fn().mockReturnValue(0.05)
                } as any;
                
                const faultyApp = createApp(faultyRiskManager, convertMockPortfolioToSrc(testPortfolio));
                
                const response = await request(faultyApp)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(500);

                expect(response.body).toMatchObject({
                    error: 'Internal Server Error',
                    message: expect.any(String),
                    timestamp: expect.any(Number)
                });
            });

            it('should handle malformed portfolio data', async () => {
                const malformedPortfolio = null as any;
                const testApp = createApp(riskManager, malformedPortfolio);
                
                const response = await request(testApp)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(500);

                expect(response.body).toMatchObject({
                    error: 'Internal Server Error',
                    message: expect.stringContaining('Cannot read properties')
                });
            });

            it('should return 404 for non-existent endpoints', async () => {
                const response = await request(app)
                    .get('/v1/risk/nonexistent')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(404);

                expect(response.body).toMatchObject({
                    error: 'Not Found',
                    message: 'Endpoint not found'
                });
            });

            it('should handle method not allowed', async () => {
                const response = await request(app)
                    .post('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(405);

                expect(response.body).toMatchObject({
                    error: 'Method Not Allowed',
                    message: 'POST method not allowed for this endpoint'
                });
            });
        });

        describe('API performance under concurrent requests', () => {
            it('should handle multiple concurrent requests efficiently', async () => {
                const concurrentRequests = 10;
                const requestPromises = Array(concurrentRequests).fill(null).map(() => 
                    request(app)
                        .get('/v1/risk/status')
                        .set('X-Skip-Rate-Limit', 'true')
                );

                const startTime = Date.now();
                const responses = await Promise.all(requestPromises);
                const endTime = Date.now();
                const totalTime = endTime - startTime;

                // All requests should succeed
                responses.forEach(response => {
                    expect(response.status).toBe(200);
                    expect(response.body).toHaveProperty('status');
                });

                // Performance should be reasonable (less than 2 seconds for 10 requests)
                expect(totalTime).toBeLessThan(2000);
            });

            it('should maintain response consistency under load', async () => {
                const responses = await Promise.all([
                    request(app).get('/v1/risk/status').set('X-Skip-Rate-Limit', 'true'),
                    request(app).get('/v1/risk/status').set('X-Skip-Rate-Limit', 'true'),
                    request(app).get('/v1/risk/status').set('X-Skip-Rate-Limit', 'true')
                ]);

                // All responses should have the same portfolio value (since portfolio is static)
                const portfolioValues = responses.map(r => r.body.portfolioValue);
                expect(new Set(portfolioValues).size).toBe(1); // All values should be identical
            });

            it('should handle rapid successive requests without degradation', async () => {
                const requestTimes: number[] = [];
                
                for (let i = 0; i < 5; i++) {
                    const startTime = Date.now();
                    await request(app)
                        .get('/v1/risk/status')
                        .set('X-Skip-Rate-Limit', 'true')
                        .expect(200);
                    const endTime = Date.now();
                    requestTimes.push(endTime - startTime);
                }

                // Response times should not significantly increase
                const avgTime = requestTimes.reduce((a, b) => a + b, 0) / requestTimes.length;
                expect(avgTime).toBeLessThan(500); // Average response time under 500ms
                
                // No request should take more than 2x the average
                requestTimes.forEach(time => {
                    expect(time).toBeLessThan(avgTime * 2);
                });
            });
        });
    });

    describe('5.2 Implement API security and validation tests', () => {
        describe('API input validation and sanitization', () => {
            it('should validate query parameters for risk status endpoint', async () => {
                const response = await request(app)
                    .get('/v1/risk/status?invalid=<script>alert("xss")</script>')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200); // Should still work but sanitize input

                // Response should not contain any script tags or malicious content
                const responseText = JSON.stringify(response.body);
                expect(responseText).not.toContain('<script>');
                expect(responseText).not.toContain('alert');
            });

            it('should handle malformed JSON in POST requests', async () => {
                const response = await request(app)
                    .post('/v1/risk/kill-switch')
                    .set('Content-Type', 'application/json')
                    .set('X-API-Key', 'valid-api-key')
                    .set('X-Skip-Rate-Limit', 'true')
                    .send('{ invalid json }')
                    .expect(400);

                expect(response.body).toMatchObject({
                    error: 'Bad Request',
                    message: expect.stringContaining('Invalid JSON')
                });
            });

            it('should sanitize input fields in kill switch requests', async () => {
                const maliciousPayload = {
                    reason: '<script>alert("xss")</script>Legitimate reason',
                    source: 'Test<img src=x onerror=alert(1)>'
                };

                const response = await request(app)
                    .post('/v1/risk/kill-switch')
                    .set('X-API-Key', 'valid-api-key')
                    .set('X-Skip-Rate-Limit', 'true')
                    .send(maliciousPayload)
                    .expect(200);

                // Response should contain sanitized content
                expect(response.body.reason).not.toContain('<script>');
                expect(response.body.reason).not.toContain('<img');
                expect(response.body.reason).toContain('Legitimate reason');
            });

            it('should validate required fields in kill switch requests', async () => {
                const response = await request(app)
                    .post('/v1/risk/kill-switch')
                    .set('X-API-Key', 'valid-api-key')
                    .set('X-Skip-Rate-Limit', 'true')
                    .send({})
                    .expect(400);

                expect(response.body).toMatchObject({
                    error: 'Bad Request',
                    message: expect.stringContaining('reason')
                });
            });

            it('should validate field length limits', async () => {
                const longReason = 'A'.repeat(1001); // Assuming 1000 char limit
                
                const response = await request(app)
                    .post('/v1/risk/kill-switch')
                    .set('X-API-Key', 'valid-api-key')
                    .set('X-Skip-Rate-Limit', 'true')
                    .send({ reason: longReason })
                    .expect(400);

                expect(response.body).toMatchObject({
                    error: 'Bad Request',
                    message: expect.stringContaining('too long')
                });
            });
        });

        describe('API authentication mechanisms', () => {
            it('should accept valid API keys', async () => {
                const response = await request(app)
                    .get('/v1/risk/status')
                    .set('X-API-Key', 'valid-api-key')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.body).toHaveProperty('status');
            });

            it('should reject invalid API keys', async () => {
                const response = await request(app)
                    .post('/v1/risk/kill-switch')
                    .set('X-API-Key', 'invalid-key')
                    .set('X-Skip-Rate-Limit', 'true')
                    .send({ reason: 'Test' })
                    .expect(401);

                expect(response.body).toMatchObject({
                    error: 'Unauthorized',
                    message: 'Invalid API key'
                });
            });

            it('should handle missing authentication for protected endpoints', async () => {
                const response = await request(app)
                    .post('/v1/risk/kill-switch')
                    .set('X-Skip-Rate-Limit', 'true')
                    .send({ reason: 'Test' })
                    .expect(401);

                expect(response.body).toMatchObject({
                    error: 'Unauthorized',
                    message: expect.stringContaining('authentication')
                });
            });

            it('should validate JWT tokens if implemented', async () => {
                const validJWT = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
                
                const response = await request(app)
                    .get('/v1/risk/status')
                    .set('Authorization', `Bearer ${validJWT}`)
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.body).toHaveProperty('status');
            });

            it('should reject expired JWT tokens', async () => {
                const expiredJWT = 'expired.jwt.token';
                
                const response = await request(app)
                    .post('/v1/risk/kill-switch')
                    .set('Authorization', `Bearer ${expiredJWT}`)
                    .set('X-Skip-Rate-Limit', 'true')
                    .send({ reason: 'Test' })
                    .expect(401);

                expect(response.body).toMatchObject({
                    error: 'Unauthorized',
                    message: expect.stringContaining('expired')
                });
            });
        });

        describe('API rate limiting and abuse prevention', () => {
            beforeEach(() => {
                // Reset rate limiting counters
                jest.clearAllMocks();
            });

            it('should allow normal request rates', async () => {
                const requests = [];
                for (let i = 0; i < 5; i++) {
                    requests.push(
                        request(app)
                            .get('/v1/risk/status')
                            .set('X-Skip-Rate-Limit', 'true')
                    );
                }

                const responses = await Promise.all(requests);
                responses.forEach(response => {
                    expect(response.status).toBe(200);
                });
            });

            it('should enforce rate limits for excessive requests', async () => {
                const requests = [];
                // Send 100 requests rapidly without skip header
                for (let i = 0; i < 100; i++) {
                    requests.push(request(app).get('/v1/risk/status'));
                }

                const responses = await Promise.all(requests);
                const rateLimitedResponses = responses.filter(r => r.status === 429);
                
                expect(rateLimitedResponses.length).toBeGreaterThan(0);
                
                rateLimitedResponses.forEach(response => {
                    expect(response.body).toMatchObject({
                        error: 'Too Many Requests',
                        message: expect.stringContaining('rate limit')
                    });
                    expect(response.headers).toHaveProperty('ratelimit-remaining');
                });
            });

            it('should have different rate limits for different endpoints', async () => {
                // Kill switch should have stricter rate limiting
                const killSwitchRequests = [];
                for (let i = 0; i < 10; i++) {
                    killSwitchRequests.push(
                        request(app)
                            .post('/v1/risk/kill-switch')
                            .set('X-API-Key', 'valid-api-key')
                            .send({ reason: `Test ${i}` })
                    );
                }

                const responses = await Promise.all(killSwitchRequests);
                const rateLimitedCount = responses.filter(r => r.status === 429).length;
                
                // Should have more rate limiting for kill switch than status endpoint
                expect(rateLimitedCount).toBeGreaterThan(5);
            });

            it('should implement IP-based rate limiting', async () => {
                const requests = [];
                for (let i = 0; i < 50; i++) {
                    requests.push(
                        request(app)
                            .get('/v1/risk/status')
                            .set('X-Forwarded-For', '*************')
                    );
                }

                const responses = await Promise.all(requests);
                const rateLimitedResponses = responses.filter(r => r.status === 429);
                
                expect(rateLimitedResponses.length).toBeGreaterThan(0);
            });

            it('should reset rate limits after time window', async () => {
                // First, hit rate limit
                const initialRequests = [];
                for (let i = 0; i < 20; i++) {
                    initialRequests.push(request(app).get('/v1/risk/status'));
                }
                await Promise.all(initialRequests);

                // Wait for rate limit window to reset (simulate with shorter time)
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Should be able to make requests again
                const response = await request(app)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.body).toHaveProperty('status');
            });
        });

        describe('API CORS configuration and security headers', () => {
            it('should include proper CORS headers', async () => {
                const response = await request(app)
                    .get('/v1/risk/status')
                    .set('Origin', 'https://trading-dashboard.example.com')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.headers).toHaveProperty('access-control-allow-origin');
                expect(response.headers['access-control-allow-origin']).toBe('https://trading-dashboard.example.com');
            });

            it('should handle preflight OPTIONS requests', async () => {
                const response = await request(app)
                    .options('/v1/risk/kill-switch')
                    .set('Origin', 'https://trading-dashboard.example.com')
                    .set('Access-Control-Request-Method', 'POST')
                    .set('Access-Control-Request-Headers', 'Content-Type')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.headers).toHaveProperty('access-control-allow-methods');
                expect(response.headers['access-control-allow-methods']).toContain('POST');
                expect(response.headers).toHaveProperty('access-control-allow-headers');
            });

            it('should reject requests from unauthorized origins', async () => {
                const response = await request(app)
                    .get('/v1/risk/status')
                    .set('Origin', 'https://malicious-site.com')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(403);

                expect(response.body).toMatchObject({
                    error: 'Forbidden',
                    message: 'Origin not allowed'
                });
            });

            it('should include security headers', async () => {
                const response = await request(app)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                // Check for important security headers
                expect(response.headers).toHaveProperty('x-content-type-options');
                expect(response.headers['x-content-type-options']).toBe('nosniff');
                
                expect(response.headers).toHaveProperty('x-frame-options');
                expect(response.headers['x-frame-options']).toBe('DENY');
                
                expect(response.headers).toHaveProperty('x-xss-protection');
                expect(response.headers['x-xss-protection']).toBe('1; mode=block');
                
                expect(response.headers).toHaveProperty('strict-transport-security');
                expect(response.headers['strict-transport-security']).toContain('max-age=');
            });

            it('should set appropriate Content-Security-Policy', async () => {
                const response = await request(app)
                    .get('/v1/risk/status')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);

                expect(response.headers).toHaveProperty('content-security-policy');
                const csp = response.headers['content-security-policy'];
                expect(csp).toContain("default-src 'self'");
                expect(csp).toContain("script-src 'self'");
            });

            it('should handle CORS for different HTTP methods', async () => {
                // Test GET
                const getResponse = await request(app)
                    .get('/v1/risk/status')
                    .set('Origin', 'https://trading-dashboard.example.com')
                    .set('X-Skip-Rate-Limit', 'true')
                    .expect(200);
                expect(getResponse.headers).toHaveProperty('access-control-allow-origin');

                // Test POST
                const postResponse = await request(app)
                    .post('/v1/risk/kill-switch')
                    .set('Origin', 'https://trading-dashboard.example.com')
                    .set('X-API-Key', 'valid-api-key')
                    .set('X-Skip-Rate-Limit', 'true')
                    .send({ reason: 'Test' })
                    .expect(200);
                expect(postResponse.headers).toHaveProperty('access-control-allow-origin');
            });
        });
    });
});