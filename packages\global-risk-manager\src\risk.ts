import { Portfolio, RiskResult } from './types';
import { KafkaService } from './kafka';
import { config } from './config';

export class RiskManager {
    private kafkaService: KafkaService;

    constructor(private readonly maxDrawdown: number, private readonly maxPositionSize: number) {
        this.kafkaService = new KafkaService([config.kafkaBroker], 'risk-manager-group');
    }

    public getMaxDrawdown(): number {
        return this.maxDrawdown;
    }

    public getMaxPositionSize(): number {
        return this.maxPositionSize;
    }

    public async connect() {
        await this.kafkaService.connect();
    }

    public async disconnect() {
        await this.kafkaService.disconnect();
    }

    public async check(portfolio: Portfolio): Promise<RiskResult> {
        // Calculate total portfolio value
        const totalValue = Array.from(portfolio.positions.values()).reduce((acc, pos) => acc + pos.amount * pos.currentPrice, portfolio.cash);
        
        // Check for max drawdown
        const initialValue = Array.from(portfolio.positions.values()).reduce((acc, pos) => acc + pos.amount * pos.entryPrice, portfolio.cash);
        const drawdown = initialValue > 0 ? Math.max(0, (initialValue - totalValue) / initialValue) : 0;
        if (drawdown > this.maxDrawdown) {
            await this.triggerKillSwitch('Max drawdown exceeded');
            return { isOk: false, reason: 'Max drawdown exceeded' };
        }

        // Check for max position size (as percentage of total portfolio)
        for (const [asset, position] of portfolio.positions) {
            const positionValue = position.amount * position.currentPrice;
            const positionRatio = positionValue / totalValue;
            if (positionRatio > this.maxPositionSize) {
                await this.triggerKillSwitch(`Max position size exceeded for ${position.asset}`);
                return { isOk: false, reason: `Max position size exceeded for ${position.asset}` };
            }
        }

        return { isOk: true };
    }

    private async triggerKillSwitch(reason: string) {
        console.log(`KILL SWITCH TRIGGERED: ${reason}`);
        await this.kafkaService.sendMessage('kill-switch', JSON.stringify({ reason }));
    }
}
